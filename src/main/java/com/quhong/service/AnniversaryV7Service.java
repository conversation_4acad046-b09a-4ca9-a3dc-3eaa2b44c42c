package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.MomentConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.AnniversaryV7DTO;
import com.quhong.data.vo.AnniversaryV7VO;
import com.quhong.data.vo.OtherSupportUserVO;
import com.quhong.data.vo.PetFeedVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *8周年活动-登录送福利抽奖活动
 */
@Service
public class AnniversaryV7Service extends OtherActivityService implements DailyTaskHandler {

    private static final Logger logger = LoggerFactory.getLogger(AnniversaryV7Service.class);
    private static final String ACTIVITY_TITLE_EN = "8th Anniversary Big Benefit";
    private static final String ACTIVITY_TITLE_SIGN_EN = "8th Anniversary Big Benefit Re Sign";
    private static final String ACTIVITY_DESC = "8th Anniversary Big Benefit Reward";
    private static final String ACTIVITY_TITLE_AR = "الذكرى الثامنة - مفاجأة كبيرة";
    private static final Integer HISTORY_USER_MAX_SIZE = 30;
    private static String ACTIVITY_ID = "dedg";
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/benefit_8th/?activityId=%s", ACTIVITY_ID);
    private static List<String> SIGN_DATE_LIST = Arrays.asList("2025-09-02", "2025-09-03", "2025-09-04", "2025-09-05", "2025-09-06", "2025-09-07", "2025-09-08");
    private static final List<String> SIGN_KEY_LIST = Arrays.asList("anniversaryV8Sign1", "anniversaryV8Sign2", "anniversaryV8Sign3", "anniversaryV8Sign4", "anniversaryV8Sign5", "anniversaryV8Sign6", "anniversaryV8Sign7");
    private static final String SIGN_KEY_FORMAT = "anniversaryV8Sign%s";// anniversaryV8Sign1~anniversaryV8Sign7
    private static final String LUCKY_KEY_FORMAT = "anniversaryV8Day%sLucky%s";// anniversaryV8Day1Lucky1~anniversaryV8Day7Lucky4
    private static final Integer LUCKY_USER_TYPE_NUM = 4; // 幸运儿类型数量

    private static final String LUCKY_KEY_USER_FORMAT = "anniversaryV8Day%sLucky%sNum%s";
    private static final Integer LUCKY_USER_NUM = 3; // 每个类型的幸运儿中奖数量
    private static final List<Integer> LUCKY_USER_TYPE_LIST = Arrays.asList(1, 2, 3, 4);

    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final String MOMENT_TEXT_EN = "Come join us in celebrating and claim your anniversary gift pack!";
    private static final String MOMENT_TEXT_AR = "انضم إلينا واحتفل واحصل على باقة الهدايا!";

    private static final String SIGN_ON_MIC_TIME = "on_mic_time";
    private static final String REMINDER_STATUS = "reminder_status"; // 签到提醒 0已关闭 1已开启
    private static final String BLESS_MSG_FIELD = "blessMsg";

    private static final String MOMENT_ORIGIN = "anniversaryV8";

    private static final String ACTIVITY_ICON = "https://cdn3.qmovies.tv/youstar/op_1756121746_fjrk.png";

    private static final String EVENT_CHECK_IN = "Log in to get the benefit lottery-check-in";
    private static final String EVENT_MAKE_UP = "Log in to get the benefit lottery-make-up";
    private static final String EVENT_WINNING = "Log in to get the benefit lottery-winning";
    @Resource
    private EventReport eventReport;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private IMomentService iMomentService;
    @Resource
    private OfficialMsgService officialMsgService;
    @Resource
    private WhiteTestDao whiteTestDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "68ad17dd37d59a020b052f29";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/benefit_8th/?activityId=%s", ACTIVITY_ID);
            SIGN_DATE_LIST = Arrays.asList("2025-08-26", "2025-08-27", "2025-08-28", "2025-08-27", "2025-08-30", "2025-08-31", "2025-09-01");
        }
    }

    private String getHashActivityId(String activityId, String uid) {
        return String.format("anniversaryV1:%s:%s", activityId, uid);
    }

    private String getDailyUserHashActivityId(String activityId, String uid, String dateStr) {
        return String.format("anniversaryV1UserDaily:%s:%s:%s", activityId, uid, dateStr);
    }

    private String getDailyHashActivityId(String activityId, String dateStr) {
        return String.format("anniversaryV1Daily:%s:%s", activityId, dateStr);
    }

    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    /**
     * 每日签到设备去重
     */
    private String getSignTnSetKey(String activityId, String dateStr) {
        return String.format("dailySignTn:%s:%s", activityId, dateStr);
    }

    /**
     * 每日参与幸运儿集合
     */
    private String getLuckySetKey(String activityId, String dateStr) {
        return String.format("dailyLucky:%s:%s", activityId, dateStr);
    }

    private String getLuckyTnSetKey(String activityId, String dateStr) {
        return String.format("dailyTnLucky:%s:%s", activityId, dateStr);
    }


    /**
     * 每日参与用户分群集合
     */
    private String getLuckyClassifySetKey(String activityId, String dateStr, int userType) {
        return String.format("dailyLuckyClassify:%s:%s:%s", activityId, dateStr, userType);
    }

    /**
     * 已经获得幸运儿的集合
     */
    private String getLuckyAlreadySetKey(String activityId) {
        return String.format("dailyLuckyAlready:%s", activityId);
    }

    /**
     * 每日参与分享的用户集合
     */
    private String getLuckyShareSetKey(String activityId, String dateStr) {
        return String.format("dailyLuckyShareClassify:%s:%s", activityId, dateStr);
    }

    // 最近的祝福语
    private String getListBlessMsgKey(String activityId) {
        return String.format("blessMsg:history:%s", activityId);
    }

    /**
     * 本地锁
     */
    private String getSignLocalKey(String uid) {
        return String.format("signAnniversary:%s", uid);
    }

    private String getLocalKey(String activityId, String uid) {
        return String.format("lock:%s:%s", activityId, uid);
    }


    private Map<String, ResourceKeyConfigData> getResourceDataMap() {
        List<ResourceKeyConfigData> resourceKeyConfigDataList = resourceKeyConfigDao.findListByKeys(new HashSet<>(SIGN_KEY_LIST));
        return resourceKeyConfigDataList.stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
    }

    public AnniversaryV7VO anniversaryV1Config(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        AnniversaryV7VO vo = new AnniversaryV7VO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));


        // String dateStr = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        String dateStr = getDayByBase(activityId, uid);
        LocalDate currentDate = LocalDate.parse(dateStr, formatter);
        vo.setCurrentDate(dateStr);

        // 用户活动数据
        String hashActivityId = this.getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        vo.setSignReminderStatus(userDataMap.getOrDefault(REMINDER_STATUS, 0));

        // 用户每日数据
        String dailyUserHashActivityId = this.getDailyUserHashActivityId(activityId, uid, dateStr);
        Map<String, String> userDailyDataMap = activityCommonRedis.getCommonHashAllMapStr(dailyUserHashActivityId);
        int userOnMicTime = Integer.parseInt(userDailyDataMap.getOrDefault(SIGN_ON_MIC_TIME, "0"));
        vo.setOnMicTime(userOnMicTime);
        vo.setFirstEntry(Integer.parseInt(userDailyDataMap.getOrDefault("firstEntry", "1")));
        if (vo.getFirstEntry() == 1) {
            activityCommonRedis.setCommonHashNum(dailyUserHashActivityId, "firstEntry", 0);
        }
        String blessMsg = activityCommonRedis.getCommonHashStrValue(dailyUserHashActivityId, BLESS_MSG_FIELD);
        vo.setBlessMsg(StringUtils.hasLength(blessMsg) ? blessMsg : "");

        List<AnniversaryV7VO.BlessMsgRecordVO> blessMsgRecordList = getHistoryBlessMsgRecord(activityId);
        vo.setBlessMsgRecordList(blessMsgRecordList);

        Map<String, ResourceKeyConfigData> resourceKeyConfigDataMap = this.getResourceDataMap();
        List<AnniversaryV7VO.SignInfo> signInfoList = new ArrayList<>();
        List<AnniversaryV7VO.DailyLuckyConfig> dailyLuckyConfigList = new ArrayList<>();
        // 签到配置相关
        for (int i = 1; i <= SIGN_DATE_LIST.size(); i++) {
            AnniversaryV7VO.SignInfo signInfo = new AnniversaryV7VO.SignInfo();
            String signDate = SIGN_DATE_LIST.get(i - 1);
            signInfo.setSignDate(signDate);
            String resKey = String.format(SIGN_KEY_FORMAT, i);
            signInfo.setResourceKeyConfigData(resourceKeyConfigDataMap.get(resKey));

            // 设置签到状态
            int curDateTnStatus = activityCommonRedis.isCommonSetData(this.getSignTnSetKey(activityId, signDate), tnId);
            LocalDate signDateFormat = LocalDate.parse(signDate, formatter);
            int signStatus = userDataMap.getOrDefault(signDate, 0);
            int signFlag = 0;
            if (currentDate.isBefore(signDateFormat)) {   // 未到日期不可签到
                signFlag = 0;
            } else if (currentDate.isAfter(signDateFormat)) {  //
                if (signStatus > 0) {
                    signFlag = 2;
                }
                if (signStatus <= 0 && userOnMicTime > 10 && curDateTnStatus <= 0) {
                    signFlag = 1;
                }
            } else {
                if (signStatus <= 0) {
                    synchronized (stringPool.intern(getSignLocalKey(uid))) {
                        if (curDateTnStatus > 0) {
                            vo.setDeviceSign(1);
                            signFlag = 0;
                        } else {
                            signFlag = 2;
                            activityCommonRedis.setCommonHashNum(hashActivityId, signDate, 1);
                            activityCommonRedis.addCommonSetData(this.getSignTnSetKey(activityId, signDate), tnId);
                            resourceKeyHandlerService.sendResourceData(uid, resKey, EVENT_CHECK_IN, EVENT_CHECK_IN, EVENT_CHECK_IN, ACTIVITY_URL, "");
                        }
                    }
                } else {
                    signFlag = 2;
                }
            }
            signInfo.setSignStatus(signFlag);
            signInfoList.add(signInfo);

            // 参与每日幸运儿相关
            AnniversaryV7VO.DailyLuckyConfig dailyLuckyConfig = new AnniversaryV7VO.DailyLuckyConfig();
            dailyLuckyConfig.setDateStr(signDate);
            dailyLuckyConfig.setLuckyNumber(activityCommonRedis.getCommonSetNum(getLuckySetKey(activityId, signDate)));
            dailyLuckyConfig.setLuckyStatus(activityCommonRedis.isCommonSetData(getLuckySetKey(activityId, signDate), uid));
            dailyLuckyConfig.setLuckyTnStatus(activityCommonRedis.isCommonSetData(getLuckyTnSetKey(activityId, signDate), tnId));

            List<AnniversaryV7VO.LuckyConfig> luckyConfigList = new ArrayList<>();
            String signDateHashActivityId = this.getDailyHashActivityId(activityId, signDate);
            Map<String, String> signDateDataMap = activityCommonRedis.getCommonHashAllMapStr(signDateHashActivityId);
            for (int j = 1; j <= LUCKY_USER_TYPE_NUM; j++) {
                AnniversaryV7VO.LuckyConfig luckyConfig = new AnniversaryV7VO.LuckyConfig();
                String luckyResKey = String.format(LUCKY_KEY_FORMAT, i, j);
                luckyConfig.setResKey(luckyResKey);
                List<OtherSupportUserVO> luckyUserList = new ArrayList<>();
                for (int k = 1; k <= LUCKY_USER_NUM; k++) {
                    String luckyUserKey = String.format(LUCKY_KEY_USER_FORMAT, i, j, k);
                    String luckyUserUid = signDateDataMap.get(luckyUserKey);
                    if (!ObjectUtils.isEmpty(luckyUserUid)) {
                        OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                        ActorData luckyActorData = actorDao.getActorDataFromCache(luckyUserUid);
                        if (k == 1) {
                            luckyConfig.setUid(luckyActorData.getUid());
                            luckyConfig.setName(luckyActorData.getName());
                            luckyConfig.setHead(ImageUrlGenerator.generateRoomUserUrl(luckyActorData.getHead()));
                        }
                        supportUserVO.setName(luckyActorData.getName());
                        supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(luckyActorData.getHead()));
                        supportUserVO.setUid(luckyActorData.getUid());
                        luckyUserList.add(supportUserVO);
                    }
                }
                luckyConfig.setLuckyUserList(luckyUserList);
                luckyConfigList.add(luckyConfig);
            }
            dailyLuckyConfig.setLuckyConfigList(luckyConfigList);
            dailyLuckyConfigList.add(dailyLuckyConfig);
        }
        vo.setSignInfoList(signInfoList);
        vo.setDailyLuckyConfigList(dailyLuckyConfigList);
        return vo;
    }

    public void anniversaryV1Sign(String activityId, String uid, String signDate) {

        checkActivityTime(activityId);
        if (!SIGN_DATE_LIST.contains(signDate)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();

        String hashActivityId = this.getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

        int signStatus = userDataMap.getOrDefault(signDate, 0);
        if (signStatus > 0) {
            throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
        }

        // String dateStr = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        String dateStr = getDayByBase(activityId, uid);

        if (activityCommonRedis.isCommonSetData(this.getSignTnSetKey(activityId, signDate), tnId) > 0) {
            logger.error("anniversaryV1Sign deviceLimit uid:{}, tn_id:{}", uid, tnId);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        LocalDate currentDate = LocalDate.parse(dateStr, formatter);
        LocalDate signDateFormat = LocalDate.parse(signDate, formatter);
        if (!signDateFormat.isBefore(currentDate)) {
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        String dailyHashActivityId = this.getDailyUserHashActivityId(activityId, uid, dateStr);
        Map<String, String> userDailyDataMap = activityCommonRedis.getCommonHashAllMapStr(dailyHashActivityId);
        int userOnMicTime = Integer.parseInt(userDailyDataMap.getOrDefault(SIGN_ON_MIC_TIME, "0"));
        if (userOnMicTime <= 10) {
            logger.info("anniversaryV1Sign userOnMicTime low uid:{}, tn_id:{} userOnMicTime:{}", uid, tnId, userOnMicTime);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        synchronized (stringPool.intern(getSignLocalKey(uid))) {
            activityCommonRedis.setCommonHashNum(hashActivityId, signDate, 1);
            activityCommonRedis.addCommonSetData(this.getSignTnSetKey(activityId, signDate), tnId);
            int index = SIGN_DATE_LIST.indexOf(signDate);
            String resKey = String.format(SIGN_KEY_FORMAT, index + 1);
            resourceKeyHandlerService.sendResourceData(uid, resKey, EVENT_MAKE_UP, EVENT_MAKE_UP, EVENT_MAKE_UP, ACTIVITY_URL, "");
        }
    }

    public void setSignReminder(String activityId, String uid, int status) {
        checkActivityTime(activityId);
        if (status != 1) {
            logger.info("invalid status. uid={}, status={}", uid, status);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "invalid status");
        }
        String hashActivityId = getHashActivityId(activityId, uid);
        activityCommonRedis.setCommonHashNum(hashActivityId, REMINDER_STATUS, status);
    }

    public void asyncSignReminderEveryDay() {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                signReminderEveryDay();
            }
        });
    }

    private void signReminderEveryDay() {
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }

        String hourStr = DateHelper.ARABIAN.formatDateInHour();
        String hour = hourStr.substring(11, 13);
        logger.info("signReminderEveryDay hour={}", hour);
        if (hour.equals("19")) {
            try {
                Set<String> allSignUserSet = new HashSet<>();
                for (String signDate : SIGN_DATE_LIST) {
                    getLuckySetKey(ACTIVITY_ID, signDate);
                    Set<String> signUserSet = activityCommonRedis.getCommonSetMember(getLuckySetKey(ACTIVITY_ID, signDate));
                    allSignUserSet.addAll(signUserSet);
                }

                String bodyEn = "Youstar 8th Anniversary Carnival, Login to get reward";
                String bodyAr = "كرنفال الذكرى الثامنة ليوستار، سجّل الدخول لتحصل على المكافأة!";
                int noticeCount = 0;
                for (String uid : allSignUserSet) {
                    String hashActivityId = getHashActivityId(ACTIVITY_ID, uid);
                    if (activityCommonRedis.getCommonHashValue(hashActivityId, REMINDER_STATUS) == 1) {
                        sendOfficialMsg(uid, "", ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, bodyEn, bodyAr, ACTIVITY_URL);
                        noticeCount++;
                    }

                }
                logger.info("signReminderEveryDay noticeCount={}", noticeCount);
            } catch (Exception e) {
                logger.error("signReminderEveryDay error: {}", e.getMessage(), e);
            }
        }
    }

    private void sendOfficialMsg(String uid, String picture, String titleEn, String titleAr, String bodyEn, String bodyAr, String url) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("sendOfficialMsg actor not found for uid: {}", uid);
            return;
        }
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleAr : titleEn;
        String body = slang == SLangType.ARABIC ? bodyAr : bodyEn;
        String actText = slang == SLangType.ARABIC ? "شاهد" : "View";
        commonOfficialMsg(uid, picture, 0, 0, actText, title, body, url, "");
    }

    // private int getLuckyUserType(String uid) {
    //     int endTime = DateHelper.getNowSeconds();
    //     int startTime = endTime - 30 * 86400;
    //     int rechargeMoney = (int) rechargeDailyInfoDao.selectUserRechargeAmount(uid, startTime);
    //     if (rechargeMoney >= 1000) {
    //         return 1;
    //     } else if (rechargeMoney >= 300) {
    //         return 2;
    //     } else if (rechargeMoney >= 1) {
    //         return 3;
    //     }
    //     return 4;
    // }

    private int getLuckyUserType(String uid) {
        //  2025.1.1–2025.8.31
        int endTime = 1756674000;
        int startTime = 1735678800;
        int rechargeMoney = (int) rechargeDailyInfoDao.getUserTotalRechargeBean(uid, startTime, endTime);
        if (rechargeMoney >= 250000) {
            return 1;
        } else if (rechargeMoney >= 50000) {
            return 2;
        } else if (rechargeMoney >= 110) {
            return 3;
        }
        return 4;
    }


    public void anniversaryV1Join(String activityId, String uid) {
        checkActivityTime(activityId);
        // String dateStr = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        String dateStr = getDayByBase(activityId, uid);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();
        if (activityCommonRedis.isCommonSetData(getLuckyTnSetKey(activityId, dateStr), tnId) > 0) {
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        activityCommonRedis.addCommonSetData(getLuckySetKey(activityId, dateStr), uid);
        if (activityCommonRedis.isCommonSetData(getLuckyAlreadySetKey(activityId), uid) <= 0) {
            int userType = this.getLuckyUserType(uid);
            activityCommonRedis.addCommonSetData(getLuckyClassifySetKey(activityId, dateStr, userType), uid);
            activityCommonRedis.addCommonSetData(getLuckyTnSetKey(activityId, dateStr), tnId);
            this.doJoinReportEvent(uid);
        }
    }

    public void anniversaryV8Join(AnniversaryV7DTO dto) {
        String activityId = dto.getActivityId();
        String uid = dto.getUid();
        String blessMsg = dto.getBlessMsg();
        int shareMoment = dto.getShareMoment();
        if (StringUtils.isEmpty(blessMsg)) {
            logger.info("anniversaryV8Join blessMsg is empty. uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "blessMsg is empty");
        }

        checkActivityTime(activityId);
        // String dateStr = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        String dateStr = getDayByBase(activityId, uid);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();
        if (activityCommonRedis.isCommonSetData(getLuckyTnSetKey(activityId, dateStr), tnId) > 0) {
            logger.error("anniversaryV8Join deviceLimit uid:{}, tn_id:{}", uid, tnId);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        activityCommonRedis.addCommonSetData(getLuckySetKey(activityId, dateStr), uid);
        if (activityCommonRedis.isCommonSetData(getLuckyAlreadySetKey(activityId), uid) <= 0) {
            int userType = this.getLuckyUserType(uid);
            activityCommonRedis.addCommonSetData(getLuckyClassifySetKey(activityId, dateStr, userType), uid);
            activityCommonRedis.addCommonSetData(getLuckyTnSetKey(activityId, dateStr), tnId);
            this.doJoinReportEvent(uid);
        }
        if (StringUtils.hasLength(blessMsg)) {
            activityCommonRedis.setCommonHashData(getDailyUserHashActivityId(activityId, uid, dateStr), BLESS_MSG_FIELD, blessMsg);
            leftPushAllBlessMsg(activityId, uid, blessMsg);
        }


        if (shareMoment == 1) {
            anniversaryV1ShareMoment(uid, activityId, actorData.getSlang());
        }
    }


    private void doJoinReportEvent(String uid) {
        ActivityApplicationEvent event = new ActivityApplicationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScene(ACTIVITY_TITLE_EN);
        eventReport.track(new EventDTO(event));
    }
    public void addShareSet(String activityId, String uid) {
        checkActivityTime(activityId);
        String dateStr = getDayByBase(activityId, uid);
        String shareSetKey = getLuckyShareSetKey(activityId, dateStr);
        if (activityCommonRedis.isCommonSetData(shareSetKey, uid) <= 0) {
            activityCommonRedis.addCommonSetData(shareSetKey, uid);
        }
    }

    // public void anniversaryV1SetDate(String activityId, String signDate) {
    //     LocalDate localDate = DateSupport.parse(signDate);
    //     LocalDate lastLocalDate = localDate.plusDays(-1);
    //     String lastDay = DateSupport.format(lastLocalDate);
    //     dailyTaskRun(lastDay);
    //     activityCommonRedis.setCommonStrData(this.getDailyDate(activityId), signDate);
    // }


    private String getLuckyUserOrderUserType(String activityId, String dateStr, int userType, Set<String> luckyAlreadySet) {
        Set<String> luckyUserTypeUidSet = activityCommonRedis.getCommonSetMember(this.getLuckyClassifySetKey(activityId, dateStr, userType));
        Set<String> shareUserSet = activityCommonRedis.getCommonSetMember(this.getLuckyShareSetKey(activityId, dateStr));

        List<String> luckyUserTypeUidList = new ArrayList<>(luckyUserTypeUidSet);
        Collections.shuffle(luckyUserTypeUidList);

        for (String luckyUserUid : luckyUserTypeUidList) {
            if (!ObjectUtils.isEmpty(luckyUserUid) && !luckyAlreadySet.contains(luckyUserUid)) {
                return luckyUserUid;
            }
        }

        String otherLuckyUid = null;
        for (Integer type : LUCKY_USER_TYPE_LIST) {
            if (type == userType) {
                continue;
            }

            Set<String> luckyUserUidSet = activityCommonRedis.getCommonSetMember(this.getLuckyClassifySetKey(activityId, dateStr, type));
            List<String> luckyUidList = new ArrayList<>(luckyUserUidSet);
            Collections.shuffle(luckyUidList);

            for (String luckyUserUid : luckyUidList) {
                if (!luckyAlreadySet.contains(luckyUserUid)) {
                    otherLuckyUid = luckyUserUid;
                    break;
                }
            }

            if (!ObjectUtils.isEmpty(otherLuckyUid)) {
                return otherLuckyUid;
            }
        }
        return otherLuckyUid;
    }


    public void anniversaryV1ShareMoment(String uid, String activityId, int slang) {

        String dateStr = getDayByBase(activityId, uid);
        String dailyUserHashActivityId = this.getDailyUserHashActivityId(activityId, uid, dateStr);
        Map<String, String> userDailyDataMap = activityCommonRedis.getCommonHashAllMapStr(dailyUserHashActivityId);
        int shareMoment = userDailyDataMap.getOrDefault("shareMoment", "0").equals("1") ? 1 : 0;
        if (shareMoment > 0) {
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE);
        }

        // 发布朋友圈
        InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();
        publishMomentDTO.setUid(uid);
        String momentText = slang == SLangType.ARABIC ? MOMENT_TEXT_AR : MOMENT_TEXT_EN;
        publishMomentDTO.setText(momentText);
        publishMomentDTO.setShow(MomentConstant.MOMENT_PUBLIC);
        publishMomentDTO.setActiveId(activityId);
        publishMomentDTO.setLocation(MOMENT_ORIGIN);
        InnerPublishMomentDTO.Quote quote = new InnerPublishMomentDTO.Quote();
        quote.setAction(ACTIVITY_URL);
        quote.setIcon(ACTIVITY_ICON);
        quote.setContent(momentText);
        quote.setType(6);
        publishMomentDTO.setQuote(quote);
        HttpResult<String> result = iMomentService.publish(publishMomentDTO);
        if (result.getCode() == 20) {
            logger.info("newYearExpect level limit. uid={} code={}", uid, result.getCode());
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }

        if (result.getCode() == 41) {
            logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
        }

        if (result.isError()) {
            logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        activityCommonRedis.incCommonHashNum(dailyUserHashActivityId, "shareMoment", 1);
    }

    public void handleMqMsg(CommonMqTopicData data) {
        String fromUid = data.getUid();
        if (!CommonMqTaskConstant.ON_MIC_TIME.equals(data.getItem())) {
            return;
        }
        if (otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        if (!checkAc(fromUid)) {
            return;
        }

        synchronized (stringPool.intern(getLocalKey(ACTIVITY_ID, fromUid))) {
            int value = data.getValue();
            // 增加积分值
            // String currentDate = activityCommonRedis.getCommonStrValue(this.getDailyDate(ACTIVITY_ID));
            String currentDate = getDayByBase(ACTIVITY_ID, fromUid);

            String dailyUserHashKey = getDailyUserHashActivityId(ACTIVITY_ID, fromUid, currentDate);
            int totalNum = activityCommonRedis.getCommonHashValue(dailyUserHashKey, SIGN_ON_MIC_TIME);

            if (totalNum >= 10) {
                return;
            }
            totalNum += value;
            activityCommonRedis.setCommonHashNum(dailyUserHashKey, SIGN_ON_MIC_TIME, totalNum);
        }
    }

    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }


    // 保存祝福记录
    private void leftPushAllBlessMsg(String activityId, String uid, String blessMsg) {
        String historyKey = getListBlessMsgKey(activityId);
        AnniversaryV7VO.BlessMsgRecordVO blessMsgRecordVO = new AnniversaryV7VO.BlessMsgRecordVO();
        blessMsgRecordVO.setAid(uid);
        blessMsgRecordVO.setBlessMsg(blessMsg);
        blessMsgRecordVO.setCtime(DateHelper.getNowSeconds());
        activityCommonRedis.addCommonListRecord(historyKey, JSONObject.toJSONString(blessMsgRecordVO));
    }

    public List<AnniversaryV7VO.BlessMsgRecordVO> getHistoryBlessMsgRecord(String activityId) {
        String key = getListBlessMsgKey(activityId);
        List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, 0, HISTORY_USER_MAX_SIZE);
        List<AnniversaryV7VO.BlessMsgRecordVO> resultList = new ArrayList<>();
        for (String json : jsonList) {
            AnniversaryV7VO.BlessMsgRecordVO rewardData = JSON.parseObject(json, AnniversaryV7VO.BlessMsgRecordVO.class);
            if (!StringUtils.isEmpty(rewardData.getAid())) {
                ActorData actorData = actorDao.getActorDataFromCache(rewardData.getAid());
                rewardData.setName(actorData.getName());
                rewardData.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            }
            resultList.add(rewardData);
        }
        return resultList;
    }

    @Override
    public void dailyTaskRun(String dateStr) {

        try {
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
            if (activityData == null) {
                return;
            }
            int endTime = activityData.getEndTime();
            int currentTime = DateHelper.getNowSeconds();
            if (currentTime - 3600 > endTime) {
                return;
            }

            if (ObjectUtils.isEmpty(dateStr)) {
                dateStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
            }
            logger.info("dailyTaskRun anniversaryV1");
            if (!SIGN_DATE_LIST.contains(dateStr)) {
                logger.error("dailyTaskRun anniversaryV1 SIGN_DATE_LIST not find dateStr:{}", dateStr);
                return;
            }

            int dayNum = SIGN_DATE_LIST.indexOf(dateStr) + 1;
            String luckyAlreadySetKey = this.getLuckyAlreadySetKey(ACTIVITY_ID);
            Set<String> luckyAlreadySet = activityCommonRedis.getCommonSetMember(luckyAlreadySetKey);

            for (int j = 1; j <= LUCKY_USER_TYPE_NUM; j++) {
                String luckyResKey = String.format(LUCKY_KEY_FORMAT, dayNum, j);
                for (int k = 1; k <= LUCKY_USER_NUM; k++) {
                    String luckyUserKey = String.format(LUCKY_KEY_USER_FORMAT, dayNum, j, k);
                    String luckyUid = this.getLuckyUserOrderUserType(ACTIVITY_ID, dateStr, j, luckyAlreadySet);
                    logger.info("dailyTaskRun anniversaryV1 dayNum:{}, userType:{}, luckyUid:{}, luckyUserKey:{}", dayNum, j, luckyUid, luckyUserKey);
                    if (!ObjectUtils.isEmpty(luckyUid)) {
                        String dailyHashActivityId = this.getDailyHashActivityId(ACTIVITY_ID, dateStr);
                        activityCommonRedis.setCommonHashData(dailyHashActivityId, luckyUserKey, luckyUid);
                        activityCommonRedis.addCommonSetData(luckyAlreadySetKey, luckyUid);
                        luckyAlreadySet.add(luckyUid);
                        this.sendOfficialData(luckyUid, luckyResKey, dateStr);
                        resourceKeyHandlerService.sendResourceData(luckyUid, luckyResKey, EVENT_WINNING, EVENT_WINNING, EVENT_WINNING, ACTIVITY_URL, "");
                    }
                }
            }
        } catch (Exception e) {
            logger.error("distribution anniversaryV1 error: {}", e.getMessage(), e);
        }
    }

    private void sendOfficialData(String luckyUid, String resKey, String dateStr) {
        ActorData actorData = actorDao.getActorDataFromCache(luckyUid);
        int slang = actorData.getSlang();
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(luckyUid);
        officialData.setTitle(slang == SLangType.ENGLISH ? ACTIVITY_TITLE_EN : ACTIVITY_TITLE_AR);
        officialData.setBody(slang == SLangType.ENGLISH ? "Congratulations on being selected as one of the lucky winners of YouStar's 8th Anniversary event. Your reward is:" : "مبروك! لقد تم اختيارك كأحد الفائزين المحظوظين في نشاط الذكرى الثامنة ليوستار. مكافأتك هي:");
        officialData.setValid(1);
        officialData.setNtype(1);
        officialData.setAtype(0);
        officialData.setNews_type(6);
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resKey);
        List<OfficialData.AwardInfo> awardList = new ArrayList<>();
        if (resourceKeyConfigData != null) {
            for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceKeyConfigData.getResourceMetaList()) {
                // OfficialData.AwardInfo awardInfo = new OfficialData.AwardInfo();
                // awardInfo.setName(slang == SLangType.ENGLISH ? resourceMeta.getResourceNameEn() : resourceMeta.getResourceNameAr());
                // awardInfo.setIcon(resourceMeta.getResourceIcon());
                // awardList.add(awardInfo);

                int resourceType = resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_COIN ? 100 : resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_DIAMOND ? 999 : resourceMeta.getResourceType();
                ResTypeEnum typeEnum = ResTypeEnum.getByType(resourceType);
                if (typeEnum == null) {
                    logger.info("sendOfficialData not find typeEnum resourceType:{}", resourceType);
                    continue;
                }
                int num = resourceMeta.getResourceNumber() > 0 ? resourceMeta.getResourceNumber() : resourceMeta.getResourceTime() > 0 ? resourceMeta.getResourceTime() : -1;
                String tag = num > 0 ? typeEnum.formatTag(slang, num) : "";
                awardList.add(new OfficialData.AwardInfo(typeEnum.getNameBySlang(slang), resourceMeta.getResourceIcon(), tag));

                LuckyUserEvent event = new LuckyUserEvent();
                event.setUid(luckyUid);
                event.setCtime(DateHelper.getNowSeconds());
                event.setDate(dateStr);
                event.setScene(ACTIVITY_TITLE_EN);
                event.setReward(resourceMeta.getResourceNameEn());
                eventReport.track(new EventDTO(event));
            }
        }
        officialData.setAward_list(awardList);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialMsgService.officialMsgPush(officialData);
    }
}
