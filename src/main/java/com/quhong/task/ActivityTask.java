package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.SpringUtils;
import com.quhong.deliver.*;
import com.quhong.service.*;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SuppressWarnings("SynchronizeOnNonFinalField")
@Component
public class ActivityTask {
    private final static Logger logger = LoggerFactory.getLogger(ActivityTask.class);
    private static final List<DailyTaskHandler> DAILY_TASK_HANDLERS = new ArrayList<>();

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private WeeklyStarDeliver weeklyStarDeliver;
    @Resource
    private RankingActivityDeliver rankingActivityDeliver;
    @Resource
    private CelebrityActivityDeliver celebrityActivityDeliver;
    @Resource
    private ConquerActivityService conquerActivityService;
    @Resource
    private OtherRankActivityDeliver otherRankActivityDeliver;
    @Resource
    private SmashEggService smashEggService;
    @Resource
    private ShareService shareService;
    @Resource
    private KnightsActivityService knightsActivityService;
    @Resource
    private GameKingActivityDeliver gameKingActivityDeliver;
    @Resource
    private RecreationTagService recreationTagService;
    @Resource
    private RoomReturnBonusService roomReturnBonusService;
    @Resource
    private NewbieStarService newbieStarService;
    @Resource
    private SuperQueen2025Service superQueen2025Service;
    @Resource
    private TaskRankTemplateDeliver taskRankTemplateDeliver;
    @Resource
    private RoomDataCenterService roomDataCenterService ;
    @Resource
    private FootballCarnivalService footballCarnivalService;
    @Resource
    private PetFeedService petFeedService;
    @Resource
    private AnniversaryV7Service anniversaryV7Service;

    @PostConstruct
    public void postInit() {
        // 初始化监听器
        Map<String, DailyTaskHandler> beans = SpringUtils.getApplicationContext().getBeansOfType(DailyTaskHandler.class);
        for (String bean : beans.keySet()) {
            DAILY_TASK_HANDLERS.add(beans.get(bean));
            logger.info("add dailyTaskHandler name={}", bean);
        }
    }

    // 每小时执行一次
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "03 00 0/1 * * ?")
    public void weeklyStar() {
        if (k8sUtils.isMasterFromCache()) {
            synchronized (weeklyStarDeliver) {
                weeklyStarDeliver.deliver();
                petFeedService.asyncSignReminderEveryDay();
                anniversaryV7Service.asyncSignReminderEveryDay();
            }
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "04 * * * * ?")
    public void rankingActivity() {
        if (k8sUtils.isMasterFromCache()) {
            synchronized (rankingActivityDeliver) {
                rankingActivityDeliver.deliver();
            }
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "05 * * * * ?")
    public void celebrityActivity() {
        if (k8sUtils.isMasterFromCache()) {
            synchronized (celebrityActivityDeliver) {
                celebrityActivityDeliver.deliver();
            }
            taskRankTemplateDeliver.deliver();
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "07 * * * * ?")
    public void cleanConquer() {
        if (k8sUtils.isMasterFromCache()) {
            synchronized (conquerActivityService) {
                conquerActivityService.cleanConquer();
            }
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "08 * * * * ?")
    public void otherRankActivity() {
        if (k8sUtils.isMasterFromCache()) {
            synchronized (otherRankActivityDeliver) {
                otherRankActivityDeliver.deliver();
            }
        }
    }


    // @Async(AsyncConfig.ASYNC_TASK)
    // @Scheduled(cron = "0 15 21 ? * SAT")
    // public void treasureWeeklyTopReward() {
    //     if (k8sUtils.isMasterFromCache()) {
    //         long millis = System.currentTimeMillis();
    //         logger.info("start execute treasureWeeklyTopReward task");
    //         treasureService.treasureWeeklyTopReward();
    //         logger.info("execute treasureWeeklyTopReward task end. cost={}", System.currentTimeMillis() - millis);
    //     }
    // }


    /**
     * 砸蛋清除时间
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "20 * * * * ?")
    public void smashEggExpireTime() {
        if (k8sUtils.isMasterFromCache()) {
            long millis = System.currentTimeMillis();
            logger.info("start execute smashEggExpireTime task");
            smashEggService.smashEggExpireTimeHandle();
            logger.info("execute smashEggExpireTime task end. cost={}", System.currentTimeMillis() - millis);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "10 01 4 * * ?")
    public void cleanShareParamData() {
        if (k8sUtils.isMasterFromCache()) {
            long millis = System.currentTimeMillis();
            logger.info("start execute cleanShareParamData task");
            shareService.cleanShareParamData();
            logger.info("execute cleanShareParamData task end. cost={}", System.currentTimeMillis() - millis);
        }
    }


    // 清除保护时间
    // @Async(AsyncConfig.ASYNC_TASK)
    // @Scheduled(cron = "10 * * * * ?")
    // public void clearKnightsProtectTime() {
    //     if (k8sUtils.isMasterFromCache()) {
    //         synchronized (knightsActivityService) {
    //             knightsActivityService.knightsProtectTimeHandle();
    //         }
    //     }
    // }

    /**
     * 每周六00:10:00执行一次游戏王活动结算
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 10 21 ? * SAT")
    public void gameKingActivity() {
        if (k8sUtils.isMasterFromCache()) {
            synchronized (gameKingActivityDeliver) {
                gameKingActivityDeliver.deliver();
            }
        }
    }

    /**
     * 每日排行榜奖励（每天北京时间05：10执行）
     */
    @Async(AsyncConfig.ASYNC_TASK)
    // @Scheduled(cron = "21 * * * * ?")
    @Scheduled(cron = "0 10 21 * * ?")
    public void everyDayRunTask() {
        if (k8sUtils.isMasterFromCache()) {
            long millis = System.currentTimeMillis();
            logger.info("start execute everyDayRunTask");
            // superPlayerService.distributionDailyRanking();
            // llluminateYouStarService.distributionRanking();
            recreationTagService.removeCommonZSetScore(DateHelper.getNowSeconds());

            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    for (DailyTaskHandler handler : DAILY_TASK_HANDLERS) {
                        try {
                            handler.dailyTaskRun(null);
                        } catch (Exception e) {
                            logger.error("dailyTaskHandler error handler={}", handler.getClass().getSimpleName(), e);
                        }
                    }
                }
            });
            logger.info("execute everyDayRunTask end. cost={}", System.currentTimeMillis() - millis);
        }
    }

    /**
     * 房间返钻每日执行（每天北京时间05：15执行）
     */
    @Async(AsyncConfig.ASYNC_TASK)
    // @Scheduled(cron = "0 0 0/1 * * ?")
    @Scheduled(cron = "0 15 21 * * ?")
    public void everyDayHandle() {
        if (k8sUtils.isMasterFromCache()) {
            long millis = System.currentTimeMillis();
            logger.info("start execute everyDayHandle");
            roomReturnBonusService.everyDayHandle();
            logger.info("execute everyDayHandle end. cost={}", System.currentTimeMillis() - millis);
        }
    }

    /**
     * 每小时运行
     */
     @Async(AsyncConfig.ASYNC_TASK)
     @Scheduled(cron = "08 03 0/1 * * ?")
     public void everyHourHandle() {
         if (k8sUtils.isMasterFromCache()) {
             long millis = System.currentTimeMillis();
             logger.info("start execute everyHourHandle");
             roomDataCenterService.fillMaxOnlineCount();
             logger.info("execute everyHourHandle end. cost={}", System.currentTimeMillis() - millis);
         }
     }


    /**
     * 每日任务（每天北京时间05：00:05执行）
     */
    @Async(AsyncConfig.ASYNC_TASK)
    // @Scheduled(cron = "21 * * * * ?")
    @Scheduled(cron = "5 0 21 * * ?")
    public void everyDayRunTask2() {
        if (k8sUtils.isMasterFromCache()) {
            long millis = System.currentTimeMillis();
            logger.info("start execute everyDayRunTask2");
            superQueen2025Service.everyDayCheck();
            logger.info("execute everyDayRunTask2 end. cost={}", System.currentTimeMillis() - millis);
        }
    }

    // 每分钟第1秒执行一次
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "01 * * * * ?")
    public void handleStageOver() {
        if (k8sUtils.isMasterFromCache()) {
            synchronized (footballCarnivalService) {
                footballCarnivalService.handleStageOver(FootballCarnivalService.ACTIVITY_ID);
            }
        }
    }
}
