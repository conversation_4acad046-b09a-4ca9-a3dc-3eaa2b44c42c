package com.quhong.data.vo;

import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class HappyTripVO {

    private Integer mileageTotalNum;            // 我的剩余程数
    private Integer tripLevel;         // 旅行等级
    private TripTeamConfigVO teamInfo;   // 团队信息
    private List<TripTeamConfigVO> teamRankList;   // 团队榜单

    private List<RecordDataVO> recordList;   // 抽奖记录列表
    private Integer nextUrl;

    private BidRewardDataVO bidRewardDataVO;   // 当前竞拍信息
    private List<BidRankingListVO> bidRankingList;   // 竞拍榜单
    private BidRankingListVO myBidRank;   // 我的竞拍排名


    public static class TripTeamConfigVO {
        private String teamId;            // 团队id
        private String teamLeadUid;       // 团队队长id
        private String teamName;          // 团队名称
        private Integer teamScore;        // 团队总分数
        private Integer rank;             // 团队排名
        private Integer muchScore;        // 分数差
        private Integer tripLevel;        // 旅行等级
        private List<TeamInfoVO> teamMemberList;  // 团队成员信息

        public String getTeamId() {
            return teamId;
        }

        public void setTeamId(String teamId) {
            this.teamId = teamId;
        }

        public String getTeamLeadUid() {
            return teamLeadUid;
        }

        public void setTeamLeadUid(String teamLeadUid) {
            this.teamLeadUid = teamLeadUid;
        }

        public String getTeamName() {
            return teamName;
        }

        public void setTeamName(String teamName) {
            this.teamName = teamName;
        }

        public Integer getTeamScore() {
            return teamScore;
        }

        public void setTeamScore(Integer teamScore) {
            this.teamScore = teamScore;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public Integer getMuchScore() {
            return muchScore;
        }

        public void setMuchScore(Integer muchScore) {
            this.muchScore = muchScore;
        }

        public List<TeamInfoVO> getTeamMemberList() {
            return teamMemberList;
        }

        public void setTeamMemberList(List<TeamInfoVO> teamMemberList) {
            this.teamMemberList = teamMemberList;
        }

        public Integer getTripLevel() {
            return tripLevel;
        }

        public void setTripLevel(Integer tripLevel) {
            this.tripLevel = tripLevel;
        }
    }

    public static class TeamInfoVO {
        private String uid;              // 用户uid
        private String name;             // 用户名
        private String head;             // 用户头像
        private Integer score;           // 驯龙次数

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getScore() {
            return score;
        }

        public void setScore(Integer score) {
            this.score = score;
        }
    }

    public static class RecordDataVO {
        private int round; // 第几轮
        private int result; // 1中奖 0未中奖
        private int bidPrice;// 我的出价或者获奖的最低出价
        private String resourceIcon;                // 资源图标
        private Integer ctime;              // 开奖时间

        public int getRound() {
            return round;
        }

        public void setRound(int round) {
            this.round = round;
        }

        public int getResult() {
            return result;
        }

        public void setResult(int result) {
            this.result = result;
        }

        public int getBidPrice() {
            return bidPrice;
        }

        public void setBidPrice(int bidPrice) {
            this.bidPrice = bidPrice;
        }

        public String getResourceIcon() {
            return resourceIcon;
        }

        public void setResourceIcon(String resourceIcon) {
            this.resourceIcon = resourceIcon;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }
    }

    public static class BidRewardDataVO extends ResourceKeyConfigData.ResourceMeta {
        private Integer startPrice; // 起拍价
        private Integer lowestPrice; // 最低价
        private Integer highestPrice; // 最高价
        private Integer roundEndTime; // 本轮截止时间
        private int myAvailableMileage; // 我的可用里程数
        private int myBidPrice; // 本轮我的出价
        private List<OtherSupportUserVO> bidUserList; // 本轮出价用户列表,取Top20


        public Integer getStartPrice() {
            return startPrice;
        }

        public void setStartPrice(Integer startPrice) {
            this.startPrice = startPrice;
        }

        public Integer getLowestPrice() {
            return lowestPrice;
        }

        public void setLowestPrice(Integer lowestPrice) {
            this.lowestPrice = lowestPrice;
        }

        public Integer getHighestPrice() {
            return highestPrice;
        }

        public void setHighestPrice(Integer highestPrice) {
            this.highestPrice = highestPrice;
        }

        public Integer getRoundEndTime() {
            return roundEndTime;
        }

        public void setRoundEndTime(Integer roundEndTime) {
            this.roundEndTime = roundEndTime;
        }

        public int getMyAvailableMileage() {
            return myAvailableMileage;
        }

        public void setMyAvailableMileage(int myAvailableMileage) {
            this.myAvailableMileage = myAvailableMileage;
        }

        public int getMyBidPrice() {
            return myBidPrice;
        }

        public void setMyBidPrice(int myBidPrice) {
            this.myBidPrice = myBidPrice;
        }

        public List<OtherSupportUserVO> getBidUserList() {
            return bidUserList;
        }

        public void setBidUserList(List<OtherSupportUserVO> bidUserList) {
            this.bidUserList = bidUserList;
        }
    }

    public static class BidRankingListVO extends OtherRankingListVO {
        private int bidNum; // 拍的藏品件数
        private String honorTitleIcon; // 荣誉称号阿语图标

        public int getBidNum() {
            return bidNum;
        }

        public void setBidNum(int bidNum) {
            this.bidNum = bidNum;
        }

        public String getHonorTitleIcon() {
            return honorTitleIcon;
        }

        public void setHonorTitleIcon(String honorTitleIcon) {
            this.honorTitleIcon = honorTitleIcon;
        }

    }

    public static class BidInfo {
        private int round; // 第几轮
        private Integer startPrice; // 起拍价
        private Integer num; // 藏品数量
        private String meteId; // 藏品id
        private Integer roundEndTime; // 本轮截止时间戳

        public int getRound() {
            return round;
        }

        public void setRound(int round) {
            this.round = round;
        }

        public Integer getStartPrice() {
            return startPrice;
        }

        public void setStartPrice(Integer startPrice) {
            this.startPrice = startPrice;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }

        public String getMeteId() {
            return meteId;
        }

        public void setMeteId(String meteId) {
            this.meteId = meteId;
        }

        public Integer getRoundEndTime() {
            return roundEndTime;
        }

        public void setRoundEndTime(Integer roundEndTime) {
            this.roundEndTime = roundEndTime;
        }
    }

    public static class BidInfoResult extends BidInfo {
        private List<String>  uidList;// 当局拍的藏品用户列表

        public List<String> getUidList() {
            return uidList;
        }

        public void setUidList(List<String> uidList) {
            this.uidList = uidList;
        }
    }

    public Integer getMileageTotalNum() {
        return mileageTotalNum;
    }

    public void setMileageTotalNum(Integer mileageTotalNum) {
        this.mileageTotalNum = mileageTotalNum;
    }

    public Integer getTripLevel() {
        return tripLevel;
    }

    public void setTripLevel(Integer tripLevel) {
        this.tripLevel = tripLevel;
    }


    public TripTeamConfigVO getTeamInfo() {
        return teamInfo;
    }

    public void setTeamInfo(TripTeamConfigVO teamInfo) {
        this.teamInfo = teamInfo;
    }

    public List<TripTeamConfigVO> getTeamRankList() {
        return teamRankList;
    }

    public void setTeamRankList(List<TripTeamConfigVO> teamRankList) {
        this.teamRankList = teamRankList;
    }


    public List<RecordDataVO> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<RecordDataVO> recordList) {
        this.recordList = recordList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }

    public BidRewardDataVO getBidRewardDataVO() {
        return bidRewardDataVO;
    }

    public void setBidRewardDataVO(BidRewardDataVO bidRewardDataVO) {
        this.bidRewardDataVO = bidRewardDataVO;
    }

    public List<BidRankingListVO> getBidRankingList() {
        return bidRankingList;
    }

    public void setBidRankingList(List<BidRankingListVO> bidRankingList) {
        this.bidRankingList = bidRankingList;
    }

    public BidRankingListVO getMyBidRank() {
        return myBidRank;
    }

    public void setMyBidRank(BidRankingListVO myBidRank) {
        this.myBidRank = myBidRank;
    }
}
